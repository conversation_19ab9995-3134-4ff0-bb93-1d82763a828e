#!/usr/bin/env python3
"""
关键词监控任务简单示例

这个示例演示如何使用关键词监控任务的基本功能：
1. 监控指定关键词的新视频
2. 自动同步视频数据到数据库
3. 创建关键词-视频关联关系
4. 更新用户订阅推送

使用方法:
1. 确保数据库已初始化并有关键词数据
2. 运行此脚本: python examples/keyword_monitor_simple_example.py
"""

import asyncio
import sys
from datetime import datetime
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from settings.config import settings
from tasks.core.models import TaskConfig
from tasks.monitors.keyword import KeywordMonitorTask
from tasks.core.logger import TaskLogger

from log import logger


class KeywordMonitorExample:
    """关键词监控简单示例"""

    def __init__(self):
        self.task_logger = TaskLogger("keyword_monitor_example")

    async def init_database(self):
        """初始化数据库连接"""
        try:
            await Tortoise.init(config=settings.tortoise_orm)
            logger.info("✅ 数据库连接初始化成功")
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {str(e)}")
            raise

    async def close_database(self):
        """关闭数据库连接"""
        try:
            await Tortoise.close_connections()
            logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.warning(f"⚠️ 关闭数据库连接时出错: {str(e)}")

    async def check_keywords_status(self):
        """检查关键词状态"""
        try:
            from models.trendinsight import TrendInsightKeyword
            
            total_count = await TrendInsightKeyword.all().count()
            logger.info(f"📊 数据库中总关键词数量: {total_count}")
            
            if total_count > 0:
                # 获取前3个关键词作为示例
                sample_keywords = await TrendInsightKeyword.all().limit(3)
                logger.info(f"📝 示例关键词:")
                for i, kw in enumerate(sample_keywords, 1):
                    logger.info(f"   {i}. {kw.keyword} (ID: {kw.id}, 更新时间: {kw.updated_at})")
                return True
            else:
                logger.warning("⚠️ 数据库中没有关键词数据，请先添加关键词")
                return False
                
        except Exception as e:
            logger.error(f"❌ 检查关键词状态失败: {str(e)}")
            return False

    async def run_basic_example(self):
        """运行基本示例"""
        logger.info(f"\n🚀 开始执行关键词监控基本示例")
        logger.info("=" * 50)
        
        # 创建任务配置
        config = TaskConfig(
            task_type="keyword_monitor",
            batch_size=10,  # 每批处理10个关键词
            max_age_hours=24,  # 监控24小时内未更新的关键词
            timeout=300,  # 5分钟超时
            filters=None  # 不使用过滤条件，监控所有关键词
        )
        
        # 创建任务实例
        task = KeywordMonitorTask(config, self.task_logger)
        
        try:
            # 执行任务
            result = await task.execute()
            
            # 显示结果
            logger.info(f"\n📊 任务执行结果:")
            logger.info(f"   状态: {result.status}")
            logger.info(f"   处理关键词数量: {result.processed_count}")
            logger.info(f"   成功数量: {result.success_count}")
            logger.info(f"   失败数量: {result.failed_count}")
            logger.info(f"   执行时间: {result.duration:.2f} 秒")
            
            if result.errors:
                logger.warning(f"   错误信息:")
                for error in result.errors[:3]:  # 只显示前3个错误
                    logger.warning(f"     - {error}")
                if len(result.errors) > 3:
                    logger.warning(f"     ... 还有 {len(result.errors) - 3} 个错误")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 任务执行失败: {str(e)}")
            return None

    async def run_filtered_example(self):
        """运行带过滤条件的示例"""
        logger.info(f"\n🎯 开始执行带过滤条件的关键词监控示例")
        logger.info("=" * 50)
        
        # 创建带过滤条件的任务配置
        config = TaskConfig(
            task_type="keyword_monitor",
            batch_size=5,  # 小批量处理
            max_age_hours=1,  # 监控1小时内未更新的关键词
            timeout=180,  # 3分钟超时
            filters={
                "keywords": ["科技", "AI", "人工智能"],  # 只监控这些关键词
                # "exclude_keywords": ["广告", "推广"]  # 排除这些关键词
            }
        )
        
        # 创建任务实例
        task = KeywordMonitorTask(config, self.task_logger)
        
        try:
            # 执行任务
            result = await task.execute()
            
            # 显示结果
            logger.info(f"\n📊 过滤任务执行结果:")
            logger.info(f"   状态: {result.status}")
            logger.info(f"   处理关键词数量: {result.processed_count}")
            logger.info(f"   成功数量: {result.success_count}")
            logger.info(f"   失败数量: {result.failed_count}")
            logger.info(f"   执行时间: {result.duration:.2f} 秒")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 过滤任务执行失败: {str(e)}")
            return None

    async def run_performance_test(self):
        """运行性能测试示例"""
        logger.info(f"\n⚡ 开始执行性能测试示例")
        logger.info("=" * 50)
        
        # 小批量快速测试配置
        config = TaskConfig(
            task_type="keyword_monitor",
            batch_size=3,  # 很小的批量
            max_age_hours=168,  # 7天范围，确保有数据
            timeout=120  # 2分钟超时
        )
        
        # 创建任务实例
        task = KeywordMonitorTask(config, self.task_logger)
        
        try:
            start_time = datetime.now()
            result = await task.execute()
            end_time = datetime.now()
            
            duration = (end_time - start_time).total_seconds()
            
            # 显示性能指标
            logger.info(f"\n⚡ 性能测试结果:")
            logger.info(f"   总耗时: {duration:.2f} 秒")
            if result.processed_count > 0:
                avg_time = duration / result.processed_count
                logger.info(f"   平均每个关键词: {avg_time:.2f} 秒")
                logger.info(f"   处理速度: {result.processed_count / duration * 60:.1f} 关键词/分钟")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 性能测试失败: {str(e)}")
            return None


async def main():
    """主函数"""
    logger.info("🔍 关键词监控任务简单示例")
    logger.info("=" * 50)
    
    example = KeywordMonitorExample()
    
    try:
        # 初始化数据库
        await example.init_database()
        
        # 检查关键词状态
        has_keywords = await example.check_keywords_status()
        
        if not has_keywords:
            logger.error("❌ 没有关键词数据，无法运行示例")
            return
        
        # 询问用户选择示例类型
        logger.info(f"\n🎯 请选择要运行的示例:")
        logger.info(f"1. 基本示例 (监控所有过期关键词)")
        logger.info(f"2. 过滤示例 (只监控指定关键词)")
        logger.info(f"3. 性能测试 (小批量快速测试)")
        logger.info(f"4. 运行所有示例")
        logger.info(f"5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == "1":
            await example.run_basic_example()
        elif choice == "2":
            await example.run_filtered_example()
        elif choice == "3":
            await example.run_performance_test()
        elif choice == "4":
            logger.info(f"\n🚀 运行所有示例...")
            await example.run_basic_example()
            await asyncio.sleep(2)
            await example.run_filtered_example()
            await asyncio.sleep(2)
            await example.run_performance_test()
        elif choice == "5":
            logger.info("👋 退出示例")
        else:
            logger.error("❌ 无效选择，退出")
            
    except KeyboardInterrupt:
        logger.info("\n⚠️ 用户中断执行")
    except Exception as e:
        logger.error(f"❌ 示例执行失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
    finally:
        # 关闭数据库连接
        await example.close_database()


if __name__ == "__main__":
    asyncio.run(main())
